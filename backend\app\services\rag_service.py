"""
RAG服务核心类
基于LlamaIndex和Qdrant实现混合检索（稀疏向量 + 密集向量）
"""
import os
from typing import List, Dict, Any, Optional
from pathlib import Path
import qdrant_client
from llama_index.core import VectorStoreIndex, StorageContext, Settings
from llama_index.vector_stores.qdrant import QdrantVectorStore
from llama_index.embeddings.openai import OpenAIEmbedding
from llama_index.llms.openai import OpenAI
from llama_index.core import SimpleDirectoryReader

import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from backend.config import settings
from backend.config.rag_optimization import setup_optimized_node_parser
from backend.config.logging_config import get_logger

logger = get_logger(__name__)


class RAGService:
    """RAG服务类，实现文档加载、索引构建和混合检索"""
    
    def __init__(self):
        self.index: Optional[VectorStoreIndex] = None
        self.query_engine = None
        self.qdrant_client = None
        self.qdrant_aclient = None  # 异步客户端
        self.vector_store = None

        # 初始化LlamaIndex设置
        self._setup_llama_index()

        # 初始化Qdrant
        self._setup_qdrant()

        # 加载现有索引或创建新索引
        self._load_or_create_index()
    
    def _setup_llama_index(self):
        """配置LlamaIndex全局设置"""
        # 强制设置环境变量
        os.environ['OPENAI_API_KEY'] = settings.openai_api_key
        os.environ['OPENAI_BASE_URL'] = settings.openai_base_url

        # 尝试monkey patch OpenAI客户端
        try:
            import openai
            # 设置全局默认值
            openai.api_key = settings.openai_api_key
            openai.base_url = settings.openai_base_url
        except Exception as e:
            logger.warning(f"设置OpenAI全局配置失败: {e}")

        # 设置LLM
        Settings.llm = OpenAI(
            api_key=settings.openai_api_key,
            base_url=settings.openai_base_url,
            model=settings.openai_model,
            temperature=0.1
        )

        # 设置嵌入模型
        Settings.embed_model = OpenAIEmbedding(
            api_key=settings.openai_api_key,
            base_url=settings.openai_base_url,
            model=settings.embedding_model,
            dimensions=1536  # 明确指定1536维，匹配ChromaDB集合期望
        )
        
        # 设置优化的文本分块器
        setup_optimized_node_parser()
        
        logger.info("LlamaIndex设置完成")
    
    def _setup_qdrant(self):
        """初始化Qdrant客户端和向量存储（启用混合检索）"""
        try:
            # 创建同步Qdrant客户端
            self.qdrant_client = qdrant_client.QdrantClient(
                host=settings.qdrant_host,
                port=settings.qdrant_port,
                api_key=settings.qdrant_api_key if settings.qdrant_api_key else None,
                prefer_grpc=settings.qdrant_prefer_grpc
            )

            # 创建异步Qdrant客户端（混合检索需要）
            self.qdrant_aclient = qdrant_client.AsyncQdrantClient(
                host=settings.qdrant_host,
                port=settings.qdrant_port,
                api_key=settings.qdrant_api_key if settings.qdrant_api_key else None,
                prefer_grpc=settings.qdrant_prefer_grpc
            )

            # 创建向量存储，启用混合检索（稀疏向量+密集向量）
            self.vector_store = QdrantVectorStore(
                client=self.qdrant_client,
                aclient=self.qdrant_aclient,
                collection_name=settings.collection_name,
                enable_hybrid=True,  # 启用混合检索
                fastembed_sparse_model="Qdrant/bm25",  # 使用BM25稀疏向量模型
                batch_size=20  # 稀疏向量生成批处理大小
            )

            logger.info(f"Qdrant客户端初始化成功: {settings.qdrant_host}:{settings.qdrant_port}")
            logger.info(f"向量存储集合: {settings.collection_name}")
            logger.info("混合检索已启用 - 密集向量(OpenAI) + 稀疏向量(BM25)")

        except Exception as e:
            logger.error(f"Qdrant初始化失败: {e}")
            raise
    
    def _load_or_create_index(self):
        """加载现有索引或创建新索引（使用Qdrant混合检索）"""
        try:
            # 创建存储上下文，使用QdrantVectorStore的内置docstore
            # 不再需要单独的持久化docstore，因为稀疏向量存储在Qdrant中
            storage_context = StorageContext.from_defaults(
                vector_store=self.vector_store
            )

            # 尝试从现有向量存储加载索引
            try:
                self.index = VectorStoreIndex.from_vector_store(
                    vector_store=self.vector_store,
                    storage_context=storage_context
                )
                logger.info("从现有Qdrant向量存储加载索引（混合检索模式）")

                # 检查Qdrant集合中的数据
                try:
                    collection_info = self.qdrant_client.get_collection(settings.collection_name)
                    points_count = collection_info.points_count
                    if points_count > 0:
                        logger.info(f"Qdrant集合包含 {points_count} 个向量点，混合检索可用")
                    else:
                        logger.warning("Qdrant集合为空，需要重新加载文档")
                except Exception as e:
                    logger.warning(f"检查Qdrant集合状态失败: {e}")

            except Exception as e:
                logger.warning(f"从向量存储加载索引失败: {e}")
                # 创建空索引
                self.index = VectorStoreIndex(
                    nodes=[],
                    storage_context=storage_context
                )
                logger.info("创建新的空索引")

            # 索引创建完成，查询引擎将在查询时动态创建
            logger.info("索引初始化完成（混合检索模式）")

        except Exception as e:
            logger.error(f"索引加载/创建失败: {e}")
            raise
    
    def _create_query_engine(self,
                             bm25_weight: float = None,
                             vector_weight: float = None,
                             similarity_top_k: int = None):
        """
        创建查询引擎，使用Qdrant原生混合检索

        Args:
            bm25_weight: BM25检索权重（稀疏向量权重）
            vector_weight: 向量检索权重（密集向量权重）
            similarity_top_k: 返回结果数量
        """
        if not self.index:
            raise ValueError("索引未初始化")

        # 使用配置默认值
        from backend.config.settings import settings
        bm25_weight = bm25_weight if bm25_weight is not None else settings.default_bm25_weight
        vector_weight = vector_weight if vector_weight is not None else settings.default_vector_weight
        similarity_top_k = similarity_top_k if similarity_top_k is not None else settings.default_similarity_top_k

        try:
            # 验证Qdrant混合检索是否可用
            if not self._validate_bm25_readiness():
                logger.warning("Qdrant混合检索不可用，回退到纯向量检索")
                query_engine = self.index.as_query_engine(
                    similarity_top_k=similarity_top_k
                )
                logger.info("创建纯向量检索查询引擎（回退模式）")
                return query_engine

            # 判断检索模式
            if bm25_weight == 0.0:
                # 纯向量检索（密集向量）
                query_engine = self.index.as_query_engine(
                    similarity_top_k=similarity_top_k
                )
                logger.info("创建纯向量检索查询引擎")

            elif vector_weight == 0.0:
                # 纯稀疏向量检索（BM25）
                query_engine = self.index.as_query_engine(
                    similarity_top_k=similarity_top_k,
                    sparse_top_k=similarity_top_k,
                    vector_store_query_mode="sparse"  # 只使用稀疏向量
                )
                logger.info("创建纯BM25检索查询引擎（稀疏向量）")

            else:
                # 混合检索（密集向量 + 稀疏向量）
                # 计算alpha权重：Qdrant使用alpha来平衡密集向量和稀疏向量
                # alpha = 0.5 表示50%密集向量 + 50%稀疏向量
                alpha = vector_weight / (vector_weight + bm25_weight)

                query_engine = self.index.as_query_engine(
                    similarity_top_k=similarity_top_k,
                    sparse_top_k=getattr(settings, 'qdrant_sparse_top_k', 10),  # 稀疏向量候选数量
                    vector_store_query_mode="hybrid",  # 启用混合检索
                    alpha=alpha  # 密集向量和稀疏向量的融合权重
                )
                logger.info(f"创建Qdrant混合检索查询引擎 (alpha={alpha:.2f}, sparse_top_k={getattr(settings, 'qdrant_sparse_top_k', 10)})")

            return query_engine

        except Exception as e:
            logger.error(f"查询引擎创建失败: {e}")
            # 回退到纯向量检索
            logger.warning("回退到纯向量检索模式")
            return self.index.as_query_engine(similarity_top_k=similarity_top_k)



    def _validate_bm25_readiness(self) -> bool:
        """
        验证Qdrant混合检索是否可以正常工作

        Returns:
            bool: True if hybrid search is ready, False otherwise
        """
        try:
            if not self.index or not self.qdrant_client:
                logger.warning("索引或Qdrant客户端未初始化，混合检索不可用")
                return False

            # 检查Qdrant集合是否存在
            try:
                collection_info = self.qdrant_client.get_collection(settings.collection_name)
            except Exception as e:
                logger.warning(f"Qdrant集合不存在或无法访问: {e}")
                return False

            # 验证是否配置了稀疏向量
            sparse_vectors_config = collection_info.config.sparse_vectors_config
            if not sparse_vectors_config or "text-sparse" not in sparse_vectors_config:
                logger.warning("Qdrant集合未配置稀疏向量，混合检索不可用")
                return False

            # 检查是否有数据
            if collection_info.points_count == 0:
                logger.warning("Qdrant集合中没有数据，混合检索不可用")
                return False

            logger.debug(f"Qdrant混合检索验证成功，集合点数: {collection_info.points_count}")
            return True

        except Exception as e:
            logger.error(f"Qdrant混合检索验证失败: {e}")
            return False

    def _validate_weights(self, bm25_weight: float, vector_weight: float) -> tuple[bool, str]:
        """
        验证权重配置的有效性

        Args:
            bm25_weight: BM25检索权重
            vector_weight: 向量检索权重

        Returns:
            tuple[bool, str]: (是否有效, 错误信息)
        """
        # 检查权重范围
        if not (0.0 <= bm25_weight <= 1.0):
            return False, f"BM25权重必须在0.0-1.0之间，当前值: {bm25_weight}"

        if not (0.0 <= vector_weight <= 1.0):
            return False, f"向量权重必须在0.0-1.0之间，当前值: {vector_weight}"

        # 检查权重总和
        total = bm25_weight + vector_weight
        if abs(total - 1.0) > 1e-6:  # 允许浮点数精度误差
            return False, f"权重总和必须等于1.0，当前总和: {total}"

        # 检查边界情况
        if bm25_weight == 0.0 and vector_weight == 0.0:
            return False, "BM25权重和向量权重不能同时为0"

        return True, ""

    def _get_retrieval_mode(self, bm25_weight: float, vector_weight: float) -> str:
        """
        根据权重确定检索模式

        Args:
            bm25_weight: BM25检索权重
            vector_weight: 向量检索权重

        Returns:
            str: 检索模式 (hybrid, vector_only, bm25_only)
        """
        if bm25_weight == 0.0:
            return "vector_only"
        elif vector_weight == 0.0:
            return "bm25_only"
        else:
            return "hybrid"

    def _fallback_to_vector_search(self, question: str, max_results: int) -> Dict[str, Any]:
        """
        回退到纯向量检索的备用方案

        Args:
            question: 用户问题
            max_results: 最大返回结果数

        Returns:
            Dict[str, Any]: 查询结果
        """
        try:
            logger.warning("使用向量检索回退方案")
            fallback_engine = self.index.as_query_engine(similarity_top_k=max_results)
            response = fallback_engine.query(question)

            # 简化的源文档处理
            sources = []
            if hasattr(response, 'source_nodes') and response.source_nodes:
                for node in response.source_nodes[:max_results]:
                    sources.append({
                        "content": node.text,
                        "content_preview": node.text[:200] + "..." if len(node.text) > 200 else node.text,
                        "score": getattr(node, 'score', 0.0),
                        "metadata": node.metadata if node.metadata else {}
                    })

            return {
                "success": True,
                "answer": str(response),
                "sources": sources,
                "total_sources": len(sources),
                "retrieval_mode": "vector_fallback",
                "weights_used": {"bm25_weight": 0.0, "vector_weight": 1.0}
            }

        except Exception as e:
            logger.error(f"向量检索回退也失败: {e}")
            return {
                "success": False,
                "message": f"所有检索方案都失败: {str(e)}",
                "answer": "",
                "sources": [],
                "retrieval_mode": "error",
                "weights_used": None
            }
    
    def load_documents(self) -> Dict[str, Any]:
        """
        加载data目录中的所有TXT文档
        实现同名文件完全替换机制
        """
        try:
            data_path = Path(settings.data_dir)
            if not data_path.exists():
                return {
                    "success": False,
                    "message": f"数据目录不存在: {data_path}",
                    "documents_processed": 0
                }
            
            # 读取所有TXT文件
            txt_files = list(data_path.glob("*.txt"))
            if not txt_files:
                return {
                    "success": False,
                    "message": "未找到TXT文件",
                    "documents_processed": 0
                }
            
            processed_files = []
            replaced_files = []
            new_files = []
            
            for txt_file in txt_files:
                filename = txt_file.name
                
                # 检查是否为同名文件（需要替换）
                existing_ids = self._get_document_ids_by_filename(filename)
                if existing_ids:
                    # 删除旧文件的所有相关数据
                    self._delete_document_by_filename(filename)
                    replaced_files.append({
                        "filename": filename,
                        "old_chunks": len(existing_ids)
                    })
                    logger.info(f"删除同名文件的旧数据: {filename}, 块数: {len(existing_ids)}")
                else:
                    new_files.append(filename)
                
                # 处理新文件
                self._process_single_file(txt_file)
                processed_files.append(filename)
            
            # 保存docstore到持久化文件
            try:
                storage_dir = Path("./storage")
                storage_dir.mkdir(exist_ok=True)
                self.index.storage_context.docstore.persist("./storage/docstore.json")
                logger.info("docstore已保存到持久化文件")
            except Exception as e:
                logger.warning(f"保存docstore失败: {e}")

            # 文档加载完成，查询引擎将在查询时动态创建
            logger.info("文档加载完成，索引已更新")
            
            # 更新replaced_files中的new_chunks信息
            for replaced_file in replaced_files:
                filename = replaced_file["filename"]
                new_ids = self._get_document_ids_by_filename(filename)
                replaced_file["new_chunks"] = len(new_ids)
            
            return {
                "success": True,
                "message": f"成功处理 {len(processed_files)} 个文件",
                "documents_processed": len(processed_files),
                "replaced_files": replaced_files,
                "new_files": new_files,
                "total_chunks": self._get_total_document_count()
            }
            
        except Exception as e:
            logger.error(f"文档加载失败: {e}")
            return {
                "success": False,
                "message": f"文档加载失败: {str(e)}",
                "documents_processed": 0
            }
    
    def _get_document_ids_by_filename(self, filename: str) -> List[str]:
        """根据文件名获取所有相关的文档ID"""
        try:
            # 使用Qdrant客户端查询
            from qdrant_client.http.models import Filter, FieldCondition, MatchValue

            search_result = self.qdrant_client.scroll(
                collection_name=settings.collection_name,
                scroll_filter=Filter(
                    must=[
                        FieldCondition(
                            key="filename",
                            match=MatchValue(value=filename)
                        )
                    ]
                ),
                limit=1000,  # 假设单个文件不会超过1000个块
                with_payload=True,
                with_vectors=False
            )

            return [str(point.id) for point in search_result[0]] if search_result[0] else []
        except Exception as e:
            logger.warning(f"查询文档ID失败: {e}")
            return []

    def _get_total_document_count(self) -> int:
        """获取总文档数量"""
        try:
            collection_info = self.qdrant_client.get_collection(settings.collection_name)
            return collection_info.points_count
        except Exception as e:
            logger.warning(f"获取文档数量失败: {e}")
            return 0
    
    def _delete_document_by_filename(self, filename: str):
        """删除指定文件名的所有相关数据（从Qdrant混合索引中删除）"""
        try:
            # 获取所有相关ID（用于统计）
            existing_ids = self._get_document_ids_by_filename(filename)

            if existing_ids:
                # 从Qdrant删除（包括密集向量和稀疏向量）
                from qdrant_client.http.models import Filter, FieldCondition, MatchValue

                self.qdrant_client.delete(
                    collection_name=settings.collection_name,
                    points_selector=Filter(
                        must=[
                            FieldCondition(
                                key="filename",
                                match=MatchValue(value=filename)
                            )
                        ]
                    )
                )

                # 注意：不再需要手动从docstore删除
                # QdrantVectorStore的混合模式会自动管理稀疏向量数据
                # 密集向量和稀疏向量都存储在同一个Qdrant集合中

                logger.info(f"从Qdrant混合索引删除文件 {filename} 的 {len(existing_ids)} 个文档块（密集+稀疏向量）")

        except Exception as e:
            logger.error(f"删除文档失败: {e}")
            raise
    
    def _process_single_file(self, file_path: Path, custom_filename: str = None, additional_metadata: Dict[str, Any] = None):
        """处理单个文件，添加到索引中"""
        import time
        start_time = time.time()
        filename = custom_filename or file_path.name
        logger.info(f"开始处理文件: {filename}")
        try:
            # 读取文档
            read_start = time.time()
            reader = SimpleDirectoryReader(
                input_files=[str(file_path)]
            )
            documents = reader.load_data()
            logger.info(f"文档读取耗时: {time.time() - read_start:.2f}秒")

            if not documents:
                logger.warning(f"文件为空或读取失败: {file_path}")
                return

            # 使用自定义文件名或原文件名
            filename = custom_filename or file_path.name

            # 为文档添加基础元数据
            for doc in documents:
                # 基础元数据
                metadata = {
                    "filename": filename,
                    "file_path": str(file_path),
                    "file_size": file_path.stat().st_size
                }
                
                # 如果是ChestnutCMS文档，添加扩展元数据
                if additional_metadata:
                    metadata.update(additional_metadata)
                
                # 如果文件名是数字.txt格式，说明是ChestnutCMS文章
                if filename.endswith('.txt') and filename[:-4].isdigit():
                    content_id = filename[:-4]
                    if "content_id" not in metadata:
                        metadata["content_id"] = content_id
                    if "source" not in metadata:
                        metadata["source"] = "chestnut_cms"
                
                doc.metadata.update(metadata)

            # 将文档转换为节点
            parse_start = time.time()
            node_parser = Settings.node_parser
            nodes = node_parser.get_nodes_from_documents(documents)
            logger.info(f"节点解析耗时: {time.time() - parse_start:.2f}秒, 生成节点数: {len(nodes)}")

            # 添加节点到Qdrant混合索引（自动生成密集向量和稀疏向量）
            insert_start = time.time()

            # QdrantVectorStore会自动处理：
            # 1. 生成密集向量（OpenAI embedding）
            # 2. 生成稀疏向量（BM25）
            # 3. 将两种向量存储在同一个Qdrant集合中
            self.index.insert_nodes(nodes)
            logger.info(f"混合向量化和插入耗时: {time.time() - insert_start:.2f}秒")
            logger.debug(f"已添加 {len(nodes)} 个节点到Qdrant混合索引（密集+稀疏向量）")

            total_time = time.time() - start_time
            logger.info(f"成功处理文件: {filename}, 总耗时: {total_time:.2f}秒")

        except Exception as e:
            logger.error(f"处理文件失败 {file_path}: {e}")
            raise
    
    def query(self,
              question: str,
              max_results: int = 5,
              bm25_weight: float = None,
              vector_weight: float = None) -> Dict[str, Any]:
        """
        执行混合检索查询

        Args:
            question: 用户问题
            max_results: 最大返回结果数
            bm25_weight: BM25检索权重
            vector_weight: 向量检索权重
        """
        import time
        start_time = time.time()

        if not self.index:
            return {
                "success": False,
                "message": "索引未初始化",
                "answer": "",
                "sources": [],
                "retrieval_mode": "error",
                "weights_used": None
            }

        try:
            # 使用配置默认值
            from backend.config.settings import settings
            bm25_weight = bm25_weight if bm25_weight is not None else settings.default_bm25_weight
            vector_weight = vector_weight if vector_weight is not None else settings.default_vector_weight

            # 验证权重配置
            is_valid, error_msg = self._validate_weights(bm25_weight, vector_weight)
            if not is_valid:
                return {
                    "success": False,
                    "message": f"权重配置无效: {error_msg}",
                    "answer": "",
                    "sources": [],
                    "retrieval_mode": "error",
                    "weights_used": {"bm25_weight": bm25_weight, "vector_weight": vector_weight}
                }

            # 确定检索模式
            retrieval_mode = self._get_retrieval_mode(bm25_weight, vector_weight)

            # 创建查询引擎
            query_engine = self._create_query_engine(
                bm25_weight=bm25_weight,
                vector_weight=vector_weight,
                similarity_top_k=max_results
            )

            # 执行查询
            response = query_engine.query(question)
            
            # 提取源文档信息
            sources = []
            if hasattr(response, 'source_nodes') and response.source_nodes:
                for node in response.source_nodes[:max_results]:
                    # 构建完整的元数据信息
                    metadata = node.metadata.copy() if node.metadata else {}
                    
                    # 构建源文档信息
                    source_info = {
                        "content": node.text,  # 提供完整的文本内容用于预览
                        "content_preview": node.text[:200] + "..." if len(node.text) > 200 else node.text,  # 保留简短预览
                        "score": getattr(node, 'score', 0.0),
                        "metadata": {
                            # 基础文件信息
                            "filename": metadata.get("filename", "未知"),
                            "file_path": metadata.get("file_path", ""),
                            
                            # ChestnutCMS扩展字段（如果存在）
                            "content_id": metadata.get("content_id"),
                            "title": metadata.get("title"),
                            "file_url": metadata.get("file_url"),
                            "publish_date": metadata.get("publish_date"),
                            "source": metadata.get("source", "local_file")
                        }
                    }
                    
                    # 移除None值的字段
                    source_info["metadata"] = {k: v for k, v in source_info["metadata"].items() if v is not None}
                    
                    sources.append(source_info)
            
            # 记录性能指标
            processing_time = time.time() - start_time
            weights_dict = {"bm25_weight": bm25_weight, "vector_weight": vector_weight}
            self._log_query_metrics(question, retrieval_mode, processing_time, len(sources), weights_dict)

            return {
                "success": True,
                "answer": str(response),
                "sources": sources,
                "total_sources": len(sources),
                "retrieval_mode": retrieval_mode,
                "weights_used": weights_dict
            }
            
        except Exception as e:
            logger.error(f"主要检索方案失败: {e}")

            # 尝试回退到纯向量检索
            if retrieval_mode != "vector_only":
                logger.info("尝试使用向量检索回退方案")
                return self._fallback_to_vector_search(question, max_results)
            else:
                # 如果已经是向量检索还失败，则返回错误
                return {
                    "success": False,
                    "message": f"查询失败: {str(e)}",
                    "answer": "",
                    "sources": [],
                    "retrieval_mode": "error",
                    "weights_used": {"bm25_weight": bm25_weight, "vector_weight": vector_weight}
                }

    def _log_query_metrics(self,
                          question: str,
                          retrieval_mode: str,
                          processing_time: float,
                          sources_count: int,
                          weights_used: dict):
        """
        记录查询性能指标

        Args:
            question: 用户问题
            retrieval_mode: 检索模式
            processing_time: 处理时间
            sources_count: 源文档数量
            weights_used: 使用的权重
        """
        logger.info(f"查询性能指标 - "
                   f"模式: {retrieval_mode}, "
                   f"耗时: {processing_time:.3f}s, "
                   f"源数量: {sources_count}, "
                   f"权重: {weights_used}, "
                   f"问题长度: {len(question)}")

        # 性能警告
        if processing_time > 10.0:
            logger.warning(f"查询耗时过长: {processing_time:.3f}s")

        if sources_count == 0:
            logger.warning("查询未返回任何相关文档")

        # 记录检索模式使用情况
        if retrieval_mode == "hybrid":
            logger.debug("使用混合检索模式")
        elif retrieval_mode == "vector_fallback":
            logger.warning("使用了回退检索模式")
        elif retrieval_mode == "error":
            logger.error("检索完全失败")
    
    def get_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        try:
            # 获取Qdrant集合信息
            doc_count = 0
            storage_size_mb = 0

            try:
                collection_info = self.qdrant_client.get_collection(settings.collection_name)
                doc_count = collection_info.points_count

                # Qdrant存储大小通过API获取（如果可用）
                # 注意：这是一个近似值，实际存储可能包含索引开销
                storage_size_mb = doc_count * 0.001  # 粗略估算，每个点约1KB

            except Exception as e:
                logger.warning(f"获取Qdrant集合信息失败: {e}")

            return {
                "status": "ok",
                "documents_count": doc_count,
                "storage_size": f"{storage_size_mb:.2f}MB",
                "collection_name": settings.collection_name,
                "data_directory": str(settings.data_dir),
                "vector_store": "Qdrant",
                "qdrant_host": f"{settings.qdrant_host}:{settings.qdrant_port}"
            }

        except Exception as e:
            logger.error(f"获取状态失败: {e}")
            return {
                "status": "error",
                "message": str(e)
            }

    def get_documents_list(self) -> Dict[str, Any]:
        """获取所有文档列表"""
        try:
            if not self.qdrant_client:
                return {
                    "success": False,
                    "message": "Qdrant客户端未初始化",
                    "documents": []
                }

            # 获取集合信息
            try:
                collection_info = self.qdrant_client.get_collection(settings.collection_name)
                total_points = collection_info.points_count
            except Exception as e:
                logger.warning(f"获取集合信息失败: {e}")
                return {
                    "success": False,
                    "message": f"集合不存在或无法访问: {str(e)}",
                    "documents": []
                }

            if total_points == 0:
                return {
                    "success": True,
                    "message": "暂无文档",
                    "documents": []
                }

            # 获取所有点的元数据（分批获取以避免内存问题）
            batch_size = 1000
            file_stats = {}
            offset = None

            while True:
                # 使用scroll API获取点
                scroll_result = self.qdrant_client.scroll(
                    collection_name=settings.collection_name,
                    limit=batch_size,
                    offset=offset,
                    with_payload=True,
                    with_vectors=False
                )

                points = scroll_result[0]
                next_offset = scroll_result[1]

                if not points:
                    break

                # 处理当前批次的点
                for point in points:
                    payload = point.payload or {}
                    filename = payload.get("filename", "未知文件")

                    if filename not in file_stats:
                        file_stats[filename] = {
                            "filename": filename,
                            "chunks_count": 0,
                            "file_size": payload.get("file_size", 0),
                            "file_path": payload.get("file_path", ""),
                            # ChestnutCMS扩展字段
                            "content_id": payload.get("content_id"),
                            "title": payload.get("title"),
                            "file_url": payload.get("file_url"),
                            "publish_date": payload.get("publish_date"),
                            "source": payload.get("source")
                        }

                    file_stats[filename]["chunks_count"] += 1

                # 检查是否还有更多数据
                if next_offset is None:
                    break
                offset = next_offset

            documents = list(file_stats.values())

            return {
                "success": True,
                "message": f"找到 {len(documents)} 个文档",
                "documents": documents,
                "total_chunks": total_points
            }

        except Exception as e:
            logger.error(f"获取文档列表失败: {e}")
            return {
                "success": False,
                "message": f"获取文档列表失败: {str(e)}",
                "documents": []
            }

    def upload_document(self, file_content: str, filename: str, additional_metadata: Dict[str, Any] = None) -> Dict[str, Any]:
        """上传单个文档"""
        try:
            # 检查文件名是否已存在
            existing_ids = self._get_document_ids_by_filename(filename)
            replaced = False
            old_chunks_count = 0

            if existing_ids:
                # 删除旧文件的所有相关数据
                old_chunks_count = len(existing_ids)
                self._delete_document_by_filename(filename)
                replaced = True
                logger.info(f"删除同名文件的旧数据: {filename}, 块数: {old_chunks_count}")

            # 确保data目录存在
            data_path = Path(settings.data_dir)
            data_path.mkdir(exist_ok=True)

            # 保存文件到data目录
            file_path = data_path / filename
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(file_content)

            logger.info(f"文件已保存到: {file_path}")

            # 处理文件（传入扩展元数据）
            self._process_single_file(file_path, additional_metadata=additional_metadata)

            # 获取新的块数量
            new_ids = self._get_document_ids_by_filename(filename)
            new_chunks_count = len(new_ids)

            return {
                "success": True,
                "message": f"文档上传成功: {filename}",
                "filename": filename,
                "replaced": replaced,
                "old_chunks": old_chunks_count if replaced else 0,
                "new_chunks": new_chunks_count,
                "total_chunks": self._get_total_document_count(),
                "file_path": str(file_path)
            }

        except Exception as e:
            logger.error(f"上传文档失败: {e}")
            return {
                "success": False,
                "message": f"上传文档失败: {str(e)}",
                "filename": filename
            }

    def delete_document(self, filename: str) -> Dict[str, Any]:
        """删除指定文档"""
        try:
            # 获取文档ID
            existing_ids = self._get_document_ids_by_filename(filename)

            if not existing_ids:
                return {
                    "success": False,
                    "message": f"文档不存在: {filename}",
                    "filename": filename
                }

            # 删除数据库中的文档
            chunks_count = len(existing_ids)
            self._delete_document_by_filename(filename)

            # 删除data目录中的文件
            data_path = Path(settings.data_dir)
            file_path = data_path / filename
            file_deleted_from_disk = False

            if file_path.exists():
                try:
                    file_path.unlink()
                    file_deleted_from_disk = True
                    logger.info(f"已从磁盘删除文件: {file_path}")
                except Exception as e:
                    logger.warning(f"删除磁盘文件失败: {e}")

            # 重新创建查询引擎
            self._create_query_engine()

            message = f"文档删除成功: {filename}"
            if file_deleted_from_disk:
                message += " (包括磁盘文件)"
            else:
                message += " (仅删除数据库记录)"

            return {
                "success": True,
                "message": message,
                "filename": filename,
                "deleted_chunks": chunks_count,
                "file_deleted_from_disk": file_deleted_from_disk,
                "total_chunks": self._get_total_document_count()
            }

        except Exception as e:
            logger.error(f"删除文档失败: {e}")
            return {
                "success": False,
                "message": f"删除文档失败: {str(e)}",
                "filename": filename
            }


# 创建全局RAG服务实例
rag_service = RAGService()
